using System;
using System.Windows.Forms;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Forms;

namespace ProManage.Tests
{
    /// <summary>
    /// Test class to verify PermissionManagementForm can be created and opened properly
    /// </summary>
    [TestClass]
    public class PermissionFormTest
    {
        [TestMethod]
        public void PermissionManagementForm_Constructor_ShouldNotThrow()
        {
            // Arrange & Act & Assert
            try
            {
                var form = new PermissionManagementForm();
                Assert.IsNotNull(form, "Form should be created successfully");
                
                // Test that the form can be disposed without errors
                form.Dispose();
            }
            catch (Exception ex)
            {
                Assert.Fail($"Form constructor should not throw exception: {ex.Message}");
            }
        }

        [TestMethod]
        public void PermissionManagementForm_LoadEvent_ShouldNotThrow()
        {
            // Arrange
            PermissionManagementForm form = null;
            
            try
            {
                form = new PermissionManagementForm();
                
                // Act - Trigger the Load event manually
                var loadMethod = typeof(PermissionManagementForm).GetMethod("PermissionManagementForm_Load", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (loadMethod != null)
                {
                    loadMethod.Invoke(form, new object[] { form, EventArgs.Empty });
                }
                
                // Assert - If we get here without exception, the test passes
                Assert.IsTrue(true, "Load event should complete without throwing");
            }
            catch (Exception ex)
            {
                // Allow certain expected exceptions during testing (like database connection issues)
                if (ex.Message.Contains("database") || ex.Message.Contains("connection"))
                {
                    Assert.Inconclusive($"Test skipped due to database dependency: {ex.Message}");
                }
                else
                {
                    Assert.Fail($"Load event should not throw unexpected exception: {ex.Message}");
                }
            }
            finally
            {
                form?.Dispose();
            }
        }

        [TestMethod]
        public void PermissionManagementForm_Properties_ShouldBeSetCorrectly()
        {
            // Arrange & Act
            using (var form = new PermissionManagementForm())
            {
                // Assert
                Assert.AreEqual("Permission Management", form.Text, "Form title should be set correctly");
                Assert.IsNotNull(form.Controls, "Form should have controls collection");
            }
        }
    }
}
