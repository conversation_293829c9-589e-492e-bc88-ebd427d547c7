using System;
using System.Collections.Generic;
using System.Diagnostics;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;
using ProManage.Modules.Testing;

namespace ProManage.Tests
{
    /// <summary>
    /// Comprehensive test suite for the complete permission system
    /// Tests role permissions, user permissions, global permissions, and form controls
    /// </summary>
    [TestClass]
    public class ComprehensivePermissionTest
    {
        private const int TestUserId = 9999;
        private const int TestRoleId = 9999;
        private const string TestFormName = "ComprehensiveTestForm";
        private const string TestRoleName = "ComprehensiveTestRole";

        [TestInitialize]
        public void Setup()
        {
            Debug.WriteLine("=== Starting Comprehensive Permission Test Setup ===");
            CleanupTestData();
            CreateTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            Debug.WriteLine("=== Starting Comprehensive Permission Test Cleanup ===");
            CleanupTestData();
        }

        [TestMethod]
        public void ComprehensivePermissionTest_AllFunctionality_WorksCorrectly()
        {
            Debug.WriteLine("=== Starting Comprehensive Permission Test ===");

            // Test 1: Role Permission Creation and Verification
            TestRolePermissions();

            // Test 2: User Permission Overrides
            TestUserPermissionOverrides();

            // Test 3: Global Permissions
            TestGlobalPermissions();

            // Test 4: FormPermissionSet Functionality
            TestFormPermissionSet();

            // Test 5: Permission Cache Performance
            TestPermissionCachePerformance();

            // Test 6: Permission Helper Validation
            TestPermissionHelperValidation();

            Debug.WriteLine("=== Comprehensive Permission Test Completed Successfully ===");
        }

        private void TestRolePermissions()
        {
            Debug.WriteLine("--- Testing Role Permissions ---");

            // Create role with specific permissions
            var permissions = new Dictionary<string, FormPermissions>
            {
                [TestFormName] = new FormPermissions
                {
                    Read = true,
                    New = false,
                    Edit = true,
                    Delete = false,
                    Print = true
                }
            };

            var roleId = PermissionTestHelper.CreateTestRole(TestRoleName, permissions);
            Assert.IsTrue(roleId > 0, "Role creation should succeed");

            // Verify role permissions are correctly set
            var hasRead = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            var hasEdit = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Edit);
            var hasDelete = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Delete);

            Assert.IsTrue(hasRead, "User should inherit read permission from role");
            Assert.IsTrue(hasEdit, "User should inherit edit permission from role");
            Assert.IsFalse(hasDelete, "User should not have delete permission from role");

            Debug.WriteLine("✓ Role permissions working correctly");
        }

        private void TestUserPermissionOverrides()
        {
            Debug.WriteLine("--- Testing User Permission Overrides ---");

            // Set user overrides that contradict role permissions
            var userOverrides = new FormPermissions
            {
                Read = false,  // Override role's true
                New = true,    // Override role's false
                Edit = false,  // Override role's true
                Delete = true, // Override role's false
                Print = null   // Inherit from role (true)
            };

            PermissionTestHelper.SetUserPermissionOverrides(TestUserId, TestFormName, userOverrides);

            // Verify user overrides take precedence
            var hasRead = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            var hasNew = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.New);
            var hasEdit = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Edit);
            var hasDelete = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Delete);
            var hasPrint = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Print);

            Assert.IsFalse(hasRead, "User override should deny read permission");
            Assert.IsTrue(hasNew, "User override should grant new permission");
            Assert.IsFalse(hasEdit, "User override should deny edit permission");
            Assert.IsTrue(hasDelete, "User override should grant delete permission");
            Assert.IsTrue(hasPrint, "User should inherit print permission from role");

            Debug.WriteLine("✓ User permission overrides working correctly");
        }

        private void TestGlobalPermissions()
        {
            Debug.WriteLine("--- Testing Global Permissions ---");

            // Set global permissions
            PermissionTestHelper.SetGlobalPermissions(TestUserId, 
                canCreateUsers: true, 
                canEditUsers: false, 
                canDeleteUsers: true, 
                canPrintUsers: false);

            // Verify global permissions
            var canCreate = PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanCreateUsers);
            var canEdit = PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanEditUsers);
            var canDelete = PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanDeleteUsers);
            var canPrint = PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanPrintUsers);

            Assert.IsTrue(canCreate, "User should have create users permission");
            Assert.IsFalse(canEdit, "User should not have edit users permission");
            Assert.IsTrue(canDelete, "User should have delete users permission");
            Assert.IsFalse(canPrint, "User should not have print users permission");

            Debug.WriteLine("✓ Global permissions working correctly");
        }

        private void TestFormPermissionSet()
        {
            Debug.WriteLine("--- Testing FormPermissionSet ---");

            // Get effective permissions as FormPermissionSet
            var permissionSet = PermissionService.GetUserEffectivePermissions(TestUserId, TestFormName);

            Assert.IsNotNull(permissionSet, "Permission set should not be null");
            Assert.AreEqual(TestFormName, permissionSet.FormName, "Form name should match");

            // Test both property naming conventions
            Assert.AreEqual(permissionSet.CanRead, permissionSet.ReadPermission, "CanRead and ReadPermission should match");
            Assert.AreEqual(permissionSet.CanEdit, permissionSet.EditPermission, "CanEdit and EditPermission should match");
            Assert.AreEqual(permissionSet.CanDelete, permissionSet.DeletePermission, "CanDelete and DeletePermission should match");
            Assert.AreEqual(permissionSet.CanPrint, permissionSet.PrintPermission, "CanPrint and PrintPermission should match");

            Debug.WriteLine("✓ FormPermissionSet working correctly");
        }

        private void TestPermissionCachePerformance()
        {
            Debug.WriteLine("--- Testing Permission Cache Performance ---");

            var stopwatch = Stopwatch.StartNew();
            const int iterations = 100;

            // Test cache performance
            for (int i = 0; i < iterations; i++)
            {
                PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
                PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanCreateUsers);
            }

            stopwatch.Stop();
            var avgTime = (double)stopwatch.ElapsedMilliseconds / iterations;

            Assert.IsTrue(avgTime < 10, $"Average permission check should be under 10ms, actual: {avgTime:F2}ms");

            Debug.WriteLine($"✓ Permission cache performance: {avgTime:F2}ms per check");
        }

        private void TestPermissionHelperValidation()
        {
            Debug.WriteLine("--- Testing Permission Helper Validation ---");

            // Run comprehensive validation
            var validationResult = PermissionTestHelper.ValidatePermissionSystem();

            Assert.IsNotNull(validationResult, "Validation result should not be null");
            Assert.IsTrue(validationResult.OverallSuccess, 
                $"Permission system validation should pass. Failed tests: {string.Join(", ", validationResult.Tests.FindAll(t => !t.Success).ConvertAll(t => t.TestName))}");

            Debug.WriteLine($"✓ Permission system validation passed ({validationResult.Tests.Count} tests)");
        }

        private void CreateTestData()
        {
            try
            {
                // Create test role
                var roleRequest = new RoleCreateRequest
                {
                    RoleName = TestRoleName,
                    Description = "Comprehensive test role",
                    IsActive = true
                };
                PermissionDatabaseService.CreateRole(roleRequest);

                Debug.WriteLine($"Created test role: {TestRoleName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Test data creation error (may already exist): {ex.Message}");
            }
        }

        private void CleanupTestData()
        {
            try
            {
                // Clean up test permissions
                PermissionDatabaseService.RemoveUserPermissions(TestUserId);
                PermissionDatabaseService.RemoveGlobalPermissions(TestUserId);
                
                // Clear cache to ensure clean state
                PermissionService.ClearCache();

                Debug.WriteLine("Test data cleanup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Cleanup error (may not exist): {ex.Message}");
            }
        }
    }
}
