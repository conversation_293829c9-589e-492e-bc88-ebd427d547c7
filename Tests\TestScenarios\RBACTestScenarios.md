# RBAC System Test Scenarios

## Manual Testing Scenarios for ProManage Permission System

### Scenario 1: Role-Based Permission Testing

**Objective**: Verify that role permissions work correctly across different user types.

**Test Steps**:
1. <PERSON><PERSON> as Administrator
2. Open Permission Management Form
3. Create a new role "TestManager" with:
   - Read permission on UserMasterForm: ✓
   - Edit permission on UserMasterForm: ✓
   - Delete permission on UserMasterForm: ✗
4. Assign a user to "TestManager" role
5. <PERSON><PERSON> as the test user
6. Verify user can:
   - Open UserMasterForm (Read permission)
   - Edit user details (Edit permission)
   - Cannot delete users (No Delete permission)

**Expected Results**:
- ✅ User can access forms with Read permission
- ✅ User can modify data with Edit permission
- ✅ Delete operations are blocked without Delete permission
- ✅ UI reflects permission restrictions (disabled buttons)

---

### Scenario 2: User Permission Override Testing

**Objective**: Verify that user-specific permissions override role permissions.

**Test Steps**:
1. Setup role "StandardUser" with NO edit permissions on EstimateForm
2. Assign user to "StandardUser" role
3. Override user permissions to ALLOW edit on EstimateForm
4. <PERSON><PERSON> as the test user
5. Verify user can edit estimates despite role restriction

**Expected Results**:
- ✅ User override takes precedence over role permission
- ✅ User can edit estimates
- ✅ Permission grid shows override with different color coding

---

### Scenario 3: Global Permission Testing

**Objective**: Verify global permissions control user management operations.

**Test Steps**:
1. Login as Administrator
2. Open UserMasterForm
3. Set global permissions for test user:
   - Can Create Users: ✗
   - Can Edit Users: ✓
   - Can Delete Users: ✗
   - Can Print Users: ✓
4. Login as test user
5. Verify user can:
   - Edit existing users
   - Print user reports
   - Cannot create new users
   - Cannot delete users

**Expected Results**:
- ✅ Global permissions properly restrict user management operations
- ✅ UI buttons are disabled based on global permissions
- ✅ Error messages appear when attempting restricted operations

---

### Scenario 4: Role Creation and Management

**Objective**: Test the role creation and management functionality.

**Test Steps**:
1. Open Permission Management Form
2. Click "Add Role" button
3. Create new role with:
   - Name: "TestRole_" + timestamp
   - Description: "Test role for validation"
   - Active: ✓
4. Set permissions for the new role
5. Assign user to the new role
6. Verify permissions work correctly

**Expected Results**:
- ✅ Role creation form opens properly
- ✅ Role is saved to database
- ✅ Role appears in permission management grids
- ✅ Users assigned to role inherit permissions

---

### Scenario 5: Permission Cache Testing

**Objective**: Verify permission caching and invalidation works correctly.

**Test Steps**:
1. Login as test user and access a form (permission cached)
2. Admin changes user's permissions
3. Test user refreshes or reopens form
4. Verify new permissions are applied immediately

**Expected Results**:
- ✅ Permission changes take effect immediately
- ✅ Cache invalidation works properly
- ✅ No stale permission data is used

---

### Scenario 6: Form Discovery and Sync

**Objective**: Test automatic form discovery and permission sync.

**Test Steps**:
1. Add a new form to MainForms folder
2. Run form discovery service
3. Verify new form appears in permission system
4. Set permissions for the new form
5. Test access to the new form

**Expected Results**:
- ✅ New forms are automatically detected
- ✅ Permission entries are created for all roles
- ✅ Permissions can be set and work correctly

---

### Scenario 7: Security Validation

**Objective**: Verify security measures and edge cases.

**Test Steps**:
1. Attempt to access forms without proper permissions
2. Try to bypass permission checks through direct URL/form access
3. Test with invalid user IDs or form names
4. Verify error handling for database connection issues

**Expected Results**:
- ✅ Unauthorized access is properly blocked
- ✅ Graceful error handling for edge cases
- ✅ No security vulnerabilities exposed
- ✅ Proper logging of security events

---

### Scenario 8: Performance Under Load

**Objective**: Test system performance with multiple users and permission checks.

**Test Steps**:
1. Create multiple test users with different roles
2. Simulate concurrent access to permission-protected forms
3. Monitor response times and system performance
4. Verify cache hit rates and performance metrics

**Expected Results**:
- ✅ Response times remain under 100ms for permission checks
- ✅ Cache hit rate above 90%
- ✅ No performance degradation with concurrent users
- ✅ Memory usage remains stable

---

## Test Data Requirements

### Test Roles
- **TestAdmin**: Full permissions on all forms
- **TestManager**: Most permissions except delete operations
- **TestUser**: Limited read/edit permissions
- **TestReadOnly**: Read-only access to most forms

### Test Users
- **testadmin**: Assigned to TestAdmin role
- **testmanager**: Assigned to TestManager role with some overrides
- **testuser**: Assigned to TestUser role
- **testreadonly**: Assigned to TestReadOnly role

### Test Forms
- UserMasterForm
- EstimateForm
- ParametersForm
- DatabaseForm

---

## Success Criteria

**All scenarios must pass with:**
- ✅ 100% functional accuracy
- ✅ Proper UI feedback and error messages
- ✅ No security vulnerabilities
- ✅ Performance within acceptable limits
- ✅ Proper logging and audit trails

**Performance Benchmarks:**
- Permission check: < 100ms
- Form load with permissions: < 500ms
- Role/permission update: < 1000ms
- Cache hit rate: > 90%

---

## Regression Testing

After any changes to the permission system:
1. Re-run all scenarios
2. Verify no existing functionality is broken
3. Test edge cases and error conditions
4. Validate performance benchmarks
5. Check security measures remain intact
