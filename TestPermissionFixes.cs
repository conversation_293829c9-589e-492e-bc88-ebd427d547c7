using System;
using System.Windows.Forms;
using ProManage.Forms;
using ProManage.Modules.Connections;
using ProManage.Tests;

namespace ProManage
{
    /// <summary>
    /// Simple test program to verify PermissionManagementForm fixes
    /// </summary>
    class TestPermissionFixes
    {
        [STAThread]
        static void Main(string[] args)
        {
            Console.WriteLine("Testing PermissionManagementForm fixes...\n");

            // Test 1: Form Constructor
            TestFormConstructor();

            // Test 2: Database Query Fixes
            TestDatabaseQueries();

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        static void TestFormConstructor()
        {
            Console.WriteLine("=== Test 1: Form Constructor ===");
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                var form = new PermissionManagementForm();
                Console.WriteLine("✓ PermissionManagementForm constructor succeeded");
                
                // Test basic properties
                Console.WriteLine($"✓ Form title: {form.Text}");
                Console.WriteLine($"✓ Form has {form.Controls.Count} controls");
                
                form.Dispose();
                Console.WriteLine("✓ Form disposed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Form constructor failed: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"  Inner exception: {ex.InnerException.Message}");
                }
            }
        }

        static void TestDatabaseQueries()
        {
            Console.WriteLine("\n=== Test 2: Database Query Fixes ===");
            try
            {
                // Run the test runner to check database fixes
                TestRunner.RunTests();
                Console.WriteLine("✓ Database tests completed - check test-results.txt for details");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Database tests failed: {ex.Message}");
            }
        }
    }
}
