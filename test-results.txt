Starting Permission System Test Runner...

=== Test 1: Database Schema Column Fix ===
✗ Database schema test failed: 42703: column "permission_id" does not exist

POSITION: 25

=== Test 2: Test User Creation ===
✗ Test user creation failed: 23502: null value in column "password_hash" of relation "users" violates not-null constraint

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.

=== Test 3: Role Creation with Unique Names ===
✓ Role created successfully with ID: 41

=== Test 4: Exception Type Handling ===
✓ First role created successfully
✓ PostgresException thrown correctly for duplicate role

=== All Tests Completed ===
Results saved to: E:\Users\Faraz\source\repos\ProManage\test-results.txt
