using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;
using Npgsql;

namespace ProManage.Tests
{
    /// <summary>
    /// Comprehensive test suite for the RBAC permission system
    /// </summary>
    [TestClass]
    public class PermissionSystemTests
    {
        private const int TestUserId = 999;
        private const int TestRoleId = 999;
        private const string TestFormName = "TestForm";

        [TestInitialize]
        public void Setup()
        {
            // Clean up any existing test data
            CleanupTestData();
            
            // Create test role and user
            CreateTestData();
        }

        [TestCleanup]
        public void Cleanup()
        {
            CleanupTestData();
        }

        #region Core Permission Tests

        [TestMethod]
        public void HasPermission_WithRolePermission_ReturnsTrue()
        {
            // Arrange
            SetupRolePermission(TestRoleId, TestFormName, read: true);
            
            // Act
            var result = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            
            // Assert
            Assert.IsTrue(result, "User should have read permission through role");
        }

        [TestMethod]
        public void HasPermission_WithUserOverride_ReturnsUserPermission()
        {
            // Arrange
            SetupRolePermission(TestRoleId, TestFormName, read: false);
            SetupUserPermission(TestUserId, TestFormName, read: true);
            
            // Act
            var result = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            
            // Assert
            Assert.IsTrue(result, "User override should take precedence over role permission");
        }

        [TestMethod]
        public void HasPermission_NoPermission_ReturnsFalse()
        {
            // Act
            var result = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            
            // Assert
            Assert.IsFalse(result, "User should not have permission when none is set");
        }

        #endregion

        #region Global Permission Tests

        [TestMethod]
        public void HasGlobalPermission_WithPermission_ReturnsTrue()
        {
            // Arrange
            SetupGlobalPermission(TestUserId, canCreateUsers: true);
            
            // Act
            var result = PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanCreateUsers);
            
            // Assert
            Assert.IsTrue(result, "User should have global create users permission");
        }

        [TestMethod]
        public void HasGlobalPermission_WithoutPermission_ReturnsFalse()
        {
            // Act
            var result = PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanCreateUsers);
            
            // Assert
            Assert.IsFalse(result, "User should not have global permission when none is set");
        }

        #endregion

        #region Role Management Tests

        [TestMethod]
        public void CreateRole_ValidRole_ReturnsSuccess()
        {
            // Arrange
            var roleRequest = new RoleCreateRequest
            {
                RoleName = "TestRole_" + Guid.NewGuid().ToString("N").Substring(0, 8),
                Description = "Test role for unit testing",
                IsActive = true
            };
            
            // Act
            var result = PermissionDatabaseService.CreateRole(roleRequest);
            
            // Assert
            Assert.IsTrue(result > 0, "Role creation should return valid role ID");
        }

        [TestMethod]
        public void CreateRole_DuplicateName_ThrowsException()
        {
            // Arrange
            var roleName = "DuplicateTestRole_" + Guid.NewGuid().ToString("N").Substring(0, 8);
            var roleRequest1 = new RoleCreateRequest { RoleName = roleName, Description = "First role" };
            var roleRequest2 = new RoleCreateRequest { RoleName = roleName, Description = "Duplicate role" };

            // Act & Assert
            PermissionDatabaseService.CreateRole(roleRequest1);
            Assert.ThrowsException<PostgresException>(() => PermissionDatabaseService.CreateRole(roleRequest2));
        }

        #endregion

        #region User Permission Tests

        [TestMethod]
        public void UpdateUserPermissions_ValidPermissions_ReturnsSuccess()
        {
            // Arrange
            var permissions = new List<UserPermissionUpdate>
            {
                new UserPermissionUpdate
                {
                    UserId = TestUserId,
                    FormName = TestFormName,
                    ReadPermission = true,
                    NewPermission = false,
                    EditPermission = true,
                    DeletePermission = false,
                    PrintPermission = true
                }
            };
            
            // Act
            var result = PermissionService.UpdateUserPermissions(TestUserId, permissions);
            
            // Assert
            Assert.IsTrue(result, "User permission update should succeed");
        }

        [TestMethod]
        public void GetUserEffectivePermissions_WithOverrides_ReturnsCorrectPermissions()
        {
            // Arrange
            SetupRolePermission(TestRoleId, TestFormName, read: false, edit: true);
            SetupUserPermission(TestUserId, TestFormName, read: true, edit: false);

            // Act
            var permissions = PermissionService.GetUserEffectivePermissions(TestUserId, TestFormName);

            // Assert
            Assert.IsTrue(permissions.CanRead, "User override should give read permission");
            Assert.IsFalse(permissions.CanEdit, "User override should deny edit permission");
        }

        #endregion

        #region Cache Tests

        [TestMethod]
        public void PermissionCache_AfterUpdate_InvalidatesCorrectly()
        {
            // Arrange
            SetupRolePermission(TestRoleId, TestFormName, read: true);
            var initialResult = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            
            // Act
            SetupRolePermission(TestRoleId, TestFormName, read: false);
            PermissionService.ClearCache();
            var updatedResult = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            
            // Assert
            Assert.IsTrue(initialResult, "Initial permission should be true");
            Assert.IsFalse(updatedResult, "Updated permission should be false after cache clear");
        }

        #endregion

        #region Performance Tests

        [TestMethod]
        public void PermissionCheck_BulkOperations_CompletesWithinTimeLimit()
        {
            // Arrange
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            const int iterations = 1000;
            
            // Act
            for (int i = 0; i < iterations; i++)
            {
                PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            }
            stopwatch.Stop();
            
            // Assert
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000, 
                $"1000 permission checks should complete within 5 seconds. Actual: {stopwatch.ElapsedMilliseconds}ms");
        }

        #endregion

        #region Helper Methods

        private void CreateTestUser()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Check if test user already exists
                    const string checkQuery = "SELECT COUNT(*) FROM users WHERE user_id = @userId";
                    using (var checkCommand = new NpgsqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@userId", TestUserId);
                        var count = Convert.ToInt32(checkCommand.ExecuteScalar());

                        if (count == 0)
                        {
                            // Create test user with role assignment
                            const string insertQuery = @"
                                INSERT INTO users (user_id, username, full_name, email, password_hash, role_id, is_active, created_date)
                                VALUES (@userId, @username, @fullName, @email, @passwordHash, @roleId, @isActive, @createdDate)";

                            using (var insertCommand = new NpgsqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@userId", TestUserId);
                                insertCommand.Parameters.AddWithValue("@username", "testuser999");
                                insertCommand.Parameters.AddWithValue("@fullName", "Test User 999");
                                insertCommand.Parameters.AddWithValue("@email", "<EMAIL>");
                                insertCommand.Parameters.AddWithValue("@passwordHash", "test_password_hash");
                                insertCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                                insertCommand.Parameters.AddWithValue("@isActive", true);
                                insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now);

                                insertCommand.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Update existing user to ensure role assignment
                            const string updateQuery = "UPDATE users SET role_id = @roleId WHERE user_id = @userId";
                            using (var updateCommand = new NpgsqlCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                                updateCommand.Parameters.AddWithValue("@userId", TestUserId);
                                updateCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating test user: {ex.Message}");
                // Continue if user creation fails
            }
        }

        private void CreateTestData()
        {
            try
            {
                // Create test role first with fixed ID
                CreateTestRole();

                // Create test user and assign to role
                CreateTestUser();
            }
            catch (Exception)
            {
                // Test data might already exist, continue
            }
        }

        private void CreateTestRole()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Check if test role already exists
                    const string checkQuery = "SELECT COUNT(*) FROM roles WHERE role_id = @roleId";
                    using (var checkCommand = new NpgsqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                        var count = Convert.ToInt32(checkCommand.ExecuteScalar());

                        if (count == 0)
                        {
                            // Create test role with fixed ID
                            const string insertQuery = @"
                                INSERT INTO roles (role_id, role_name, description, is_active, created_date)
                                VALUES (@roleId, @roleName, @description, @isActive, @createdDate)";

                            using (var insertCommand = new NpgsqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                                insertCommand.Parameters.AddWithValue("@roleName", $"TestRole_{TestRoleId}");
                                insertCommand.Parameters.AddWithValue("@description", "Test role for unit testing");
                                insertCommand.Parameters.AddWithValue("@isActive", true);
                                insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now);

                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating test role: {ex.Message}");
                // Continue if role creation fails
            }
        }

        private void CleanupTestData()
        {
            try
            {
                // Clean up test permissions
                PermissionDatabaseService.RemoveUserPermissions(TestUserId);
                PermissionDatabaseService.RemoveGlobalPermissions(TestUserId);

                // Clean up test user
                CleanupTestUser();

                // Clean up test role
                CleanupTestRole();
            }
            catch (Exception)
            {
                // Cleanup might fail if data doesn't exist, continue
            }
        }

        private void CleanupTestRole()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Remove role permissions first
                    const string deletePermissionsQuery = "DELETE FROM role_permissions WHERE role_id = @roleId";
                    using (var permCommand = new NpgsqlCommand(deletePermissionsQuery, connection))
                    {
                        permCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                        permCommand.ExecuteNonQuery();
                    }

                    // Remove test role
                    const string deleteRoleQuery = "DELETE FROM roles WHERE role_id = @roleId";
                    using (var roleCommand = new NpgsqlCommand(deleteRoleQuery, connection))
                    {
                        roleCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                        roleCommand.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning up test role: {ex.Message}");
                // Continue if cleanup fails
            }
        }

        private void CleanupTestUser()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Remove test user
                    const string deleteQuery = "DELETE FROM users WHERE user_id = @userId";
                    using (var command = new NpgsqlCommand(deleteQuery, connection))
                    {
                        command.Parameters.AddWithValue("@userId", TestUserId);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning up test user: {ex.Message}");
                // Continue if cleanup fails
            }
        }

        private void SetupRolePermission(int roleId, string formName, bool read = false, bool edit = false, bool delete = false, bool print = false)
        {
            // Role should already exist from CreateTestData, no need to create again
            var permission = new RolePermissionUpdate
            {
                RoleId = roleId,
                FormName = formName,
                ReadPermission = read,
                NewPermission = false,
                EditPermission = edit,
                DeletePermission = delete,
                PrintPermission = print
            };

            PermissionService.UpdateRolePermissions(roleId, new List<RolePermissionUpdate> { permission });
        }

        private void SetupUserPermission(int userId, string formName, bool? read = null, bool? edit = null, bool? delete = null, bool? print = null)
        {
            var permission = new UserPermissionUpdate
            {
                UserId = userId,
                FormName = formName,
                ReadPermission = read,
                NewPermission = null,
                EditPermission = edit,
                DeletePermission = delete,
                PrintPermission = print
            };
            
            PermissionService.UpdateUserPermissions(userId, new List<UserPermissionUpdate> { permission });
        }

        private void SetupGlobalPermission(int userId, bool canCreateUsers = false, bool canEditUsers = false, bool canDeleteUsers = false, bool canPrintUsers = false)
        {
            var globalPermission = new GlobalPermissionUpdate
            {
                UserId = userId,
                CanCreateUsers = canCreateUsers,
                CanEditUsers = canEditUsers,
                CanDeleteUsers = canDeleteUsers,
                CanPrintUsers = canPrintUsers
            };
            
            PermissionService.UpdateGlobalPermissions(userId, globalPermission);
        }

        #endregion
    }
}
